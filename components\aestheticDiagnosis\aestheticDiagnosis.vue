<template>
  <view
    class="w-full max-w-[750rpx] min-h-screen flex flex-col items-center justify-between mx-auto pt-0 ios-safe-area">
    <!-- 上半部分：图片展示 -->

    <view style="transition: all 0.3s;justify-content: center;" class="w-full flex-1 flex flex-col relative topImg">
      <view v-if="!generate.length" class="image-container" style="position: relative; display: inline-block; overflow: hidden;">
        <image class="beforeimg" ref="beforeimgRef" :src="props.peopleImg" mode="widthFix"
          :style="imageStyle" @load="onImageLoad" />
        <canvas v-if="showCanvas" canvas-id="eyeCanvas" id="eyeCanvas" :style="canvasStyle"
          style="position: absolute; top: 0; left: 0; pointer-events: none;"></canvas>
        <!-- 光点动画容器 -->
        <view v-for="(animation, index) in loadingAnimations" :key="animation.id || index" class="light-point" :style="{
          left: animation.x + 'px',
          top: animation.y + 'px',
          opacity: animation.visible ? 1 : 0
        }"></view>
      </view>
      <ImgCompare v-else :before="props.peopleImg" :after="generate" :height="1000" />
    </view>
    <view v-if="!!props.activeReport" class="popup-container"
      :class="[popupVisible ? 'slide-up' : 'slide-down', isExpanded ? 'expanded' : 'collapsed']" @click.stop
      @touchstart.passive @touchmove.passive>
      <!-- <view class="popup-header">
          <text class="popup-title"></text>
          <uni-icons type="closeempty" size="24" color="#666" @click="closePopup"></uni-icons>
        </view> -->

      <view v-if="props.activeReport == '诊断报告'" class=" ">
        <button @click="handlePreview()" style="margin: 0 auto;padding: 0;line-height: 30rpx;"
          class="bg-[#FF8F9C] text-white text-xs rounded  w-10 py-1 font-medium flex justify-center ios-button">
          <up-icon v-if="!isExpanded" size="20" color="#fff" name="arrow-up"></up-icon>
          <up-icon v-else size="20" color="#fff" name="arrow-down"></up-icon>
        </button>
        <view class="popup-content py-1 px-6 m-2 " v-if="'defect_report' in face_aes">
          <text class="text-sm mb-1 block" style="font-size: 28rpx;font-weight: 500;">下巴</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.chin.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">轮廓</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.contour.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">眼睛</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.eyes.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">额头</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.forehead.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">嘴巴</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.mouth.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">鼻子</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.nose.description }}
          </text>
          <view>
            您的总体评分：<view class="ml-4" style="display: inline-flex;">
              {{ face_aes.overall_score }}
            </view>
          </view>
        </view>
      </view>
      <template v-if="props.activeReport == '美学方案'">
        <button @click="handlePreview()" style="margin: 0 auto;padding: 0;line-height: 30rpx;"
          class="bg-[#FF8F9C] text-white text-xs rounded  w-10 py-1 font-medium flex justify-center ios-button">
          <up-icon v-if="!isExpanded" size="20" color="#fff" name="arrow-up"></up-icon>
          <up-icon v-else size="20" color="#fff" name="arrow-down"></up-icon>
        </button>
        <view class="popup-content py-1 px-6 m-2 ">
          <template v-for="item in face_aes.treatment_plans.plan_a.projects" :key="item.name">
            <text class="text-sm mb-1 block" style="font-size: 28rpx;font-weight: 500;">{{ item.name }}</text>
            <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
              {{ item.reason }}
            </text>
          </template>
          <text class="text-sm mb-1 block" style="font-size: 28rpx;font-weight: 500;">总结：</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.treatment_plans.plan_a.reason }}
          </text>
        </view>
      </template>

      <scroll-view v-if="props.activeReport == '专属推荐'" class="popup-content" scroll-x style="white-space: nowrap;">
        <view class="flex flex-nowrap" style="width: max-content;">
          <view class="flex items-center mx-2 p-2 bg-[#F8F8F8]" v-for="(item, index) in recommendedItems"
            @click="gotoOrgList" :key="index">
            <div class="flex flex-col flex-1">
              <div class="text-[14px] text-[#222] leading-tight" style="font-family: 'PingFang SC', Arial;">{{
                item.title }}</div>
              <div class="text-[12px] text-[#999] mt-1" style="font-family: 'PingFang SC', Arial;">{{
                item.category }}
                <span>{{ item.location }}</span>
              </div>
            </div>

            <img :src="item.imgSrc" :alt="item.alt" class="w-14 h-14 rounded-lg object-cover ml-3" />
          </view>
        </view>
      </scroll-view>

    </view>




  </view>
  <!-- 底部导航栏2 -->
  <view v-show="props.activeReport" style="z-index: 5;"
    class="fixed bottom-0 left-0 w-full bg-[#fff] border-t border-[#f0f0f0] transition-transform duration-300 transform ios-bottom-safe"
    :class="{ 'translate-y-0': props.activeReport, 'translate-y-full': !props.activeReport }">
    <view class="flex justify-around items-center h-16 px-4">
      <view v-for="item in reports" style="font-size: 28rpx;border-color: #F39196;" class="font-semi"
        :class="props.activeReport == item ? 'font-semibold border-b-2' : ''" @click="selectIcon2(item)">{{ item
        }}</view>
    </view>
  </view>

</template>

<script setup>
import { useStore } from "vuex"
import ImgCompare from '@/components/imgCompare.vue';
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { callAiPolling } from "@/Api/index.js"
const props = defineProps({
  compareFlag: {
    type: Boolean,
    default: false
  },
  peopleImg: {
    type: String,
    default: ''
  },

  buttonIndex: {
    type: Number,
    default: -1
  },
  activeReport: {
    type: String,
    default: ''
  },
  icons: {
    type: Array,
    default: []
  },
  reports: {
    type: Array,
    default: []
  },

});
let timer = null

// 图片加载完成事件处理
const onImageLoad = () => {
  // 图片加载完成后处理detectInfo
  let detectInfo = uni.getStorageSync('detectInfo')
  if (detectInfo && detectInfo.detect) {
    // 检查detectInfo.detect是否为字符串，如果是则解析
    if (typeof detectInfo.detect === 'string') {
      detectInfo.detect = JSON.parse(detectInfo.detect).faces[0]
    }
    console.log('detectInfo.detect:', detectInfo.detect);
    console.log('facial_angles:', detectInfo.detect.facial_angles);
    console.log('chin_angle:', detectInfo.detect.facial_angles?.chin_angle);

    // 计算图片宽度比值和处理landmarks
    processDetectInfo(detectInfo)
  }
}

onMounted(() => {

  // emit('update:loading', true)
  // iOS兼容性：初始化状态
  isExpanded.value = false
  isAnimating.value = false
  popupVisible.value = false

  let label = props.icons[0].reports[0];
  selectIcon2(label);
  let operationId = uni.getStorageSync('operationId')
  if (operationId) {
    getStatus(operationId)

    timer = setInterval(async () => {
      getStatus(operationId)
    }, 3000)
  }
})
onBeforeUnmount(() => {

  clearInterval(timer)
})
const emit = defineEmits(['update:activeReport', 'update:loading', 'update:percent', 'show-login']);

const beforeImgHeight = ref(0) // 原图高度
const beforeimgRef = ref(null) // 图片引用
const isExpanded = ref(false) // 控制弹窗展开状态
const isAnimating = ref(false) // 控制动画状态，防止iOS端快速点击

// 处理detectInfo数据
const processDetectInfo = (detectInfo) => {
  // 使用nextTick确保DOM已渲染，然后获取图片实际尺寸
  nextTick(() => {
    setTimeout(() => {
      // 获取图片元素的实际显示尺寸
      const query = uni.createSelectorQuery()
      query.select('.beforeimg').boundingClientRect((rect) => {

        if (rect) {
          const displayWidth = rect.width
          const displayHeight = rect.height

          // 计算宽高比值
          const widthRatio = displayWidth / detectInfo.imgWidth
          const heightRatio = displayHeight / detectInfo.imgHeight

          // 处理landmarks数据，按part分组，并对坐标进行比值计算
          const landmarksObj = {}
          if (detectInfo.detect && detectInfo.detect.landmarks) {
            detectInfo.detect.landmarks.forEach(landmark => {
              const part = landmark.part
              if (!landmarksObj[part]) {
                landmarksObj[part] = []
              }
              landmarksObj[part].push({
                x: landmark.x * widthRatio,  // 对x坐标进行宽度比值计算
                y: landmark.y * heightRatio, // 对y坐标进行高度比值计算
                originalX: landmark.x,       // 保留原始x坐标
                originalY: landmark.y,       // 保留原始y坐标
                id: landmark.id
              })
            })
          }

          // 存储下巴角度数据
          chinAngleData.value = detectInfo.detect.facial_angles ? detectInfo.detect.facial_angles.chin_angle : null

          // 将数据存储到响应式变量中供后续使用
          imageWidthRatio.value = widthRatio
          imageHeightRatio.value = heightRatio
          landmarksData.value = landmarksObj

          // 初始化canvas绘制
          initCanvasDrawing(displayWidth, displayHeight, landmarksObj)
        } else {
          // console.error('无法获取图片元素尺寸')
        }
      }).exec()
    }, 1000);
  })
}

// 响应式变量存储处理后的数据
const imageWidthRatio = ref(0)
const imageHeightRatio = ref(0)
const landmarksData = ref({})
const chinAngleData = ref(null)

// Canvas相关变量
const showCanvas = ref(false)
const canvasStyle = ref({})
const imageStyle = ref({ width: '100%' })
const loadingAnimations = ref([])
const animationFrames = ref([])

// Canvas绘制和动画相关方法

// 计算路径总长度
const calculatePathLength = (points) => {
  let length = 0;
  for (let i = 0; i < points.length - 1; i++) {
    const dx = points[i + 1].x - points[i].x;
    const dy = points[i + 1].y - points[i].y;
    length += Math.sqrt(dx * dx + dy * dy);
  }
  // 闭合路径
  const dx = points[0].x - points[points.length - 1].x;
  const dy = points[0].y - points[points.length - 1].y;
  length += Math.sqrt(dx * dx + dy * dy);
  return length;
}

// 根据路径长度比例获取对应点
const getPointAtLength = (points, pathLength, targetLength) => {
  let accumulatedLength = 0;

  // 检查闭合路径的最后一段
  const lastSegmentLength = Math.sqrt(
    Math.pow(points[0].x - points[points.length - 1].x, 2) +
    Math.pow(points[0].y - points[points.length - 1].y, 2)
  );

  // 先检查是否在最后一段
  if (targetLength >= pathLength - lastSegmentLength) {
    const progress = (targetLength - (pathLength - lastSegmentLength)) / lastSegmentLength;
    return {
      x: points[points.length - 1].x + (points[0].x - points[points.length - 1].x) * progress,
      y: points[points.length - 1].y + (points[0].y - points[points.length - 1].y) * progress
    };
  }

  // 检查其他段
  for (let i = 0; i < points.length - 1; i++) {
    const segmentLength = Math.sqrt(
      Math.pow(points[i + 1].x - points[i].x, 2) +
      Math.pow(points[i + 1].y - points[i].y, 2)
    );

    if (targetLength <= accumulatedLength + segmentLength) {
      const progress = (targetLength - accumulatedLength) / segmentLength;
      return {
        x: points[i].x + (points[i + 1].x - points[i].x) * progress,
        y: points[i].y + (points[i + 1].y - points[i].y) * progress
      };
    }
    accumulatedLength += segmentLength;
  }

  return points[0]; // 默认返回起点
}

// 创建轮廓光点动画
const createOutlineAnimation = (points, duration = 3000) => {
  const animationId = Date.now() + Math.random();
  const pathLength = calculatePathLength(points);
  let startTime = null;

  // 创建光点元素数据
  const lightPoint = {
    id: animationId,
    x: points[0].x,
    y: points[0].y,
    visible: true
  };

  loadingAnimations.value.push(lightPoint);

  const animate = (timestamp) => {
    if (!startTime) startTime = timestamp;
    const elapsed = timestamp - startTime;

    // 计算归一化的进度(0-1)
    const progress = (elapsed % duration) / duration;

    // 计算当前路径长度位置
    const currentLength = progress * pathLength;

    // 获取对应坐标点
    const position = getPointAtLength(points, pathLength, currentLength);

    // 更新光点位置
    const pointIndex = loadingAnimations.value.findIndex(p => p.id === animationId);
    if (pointIndex !== -1) {
      loadingAnimations.value[pointIndex].x = position.x;
      loadingAnimations.value[pointIndex].y = position.y;
    }

    // 继续动画
    const frameId = requestAnimationFrame(animate);
    animationFrames.value.push(frameId);
  };

  const frameId = requestAnimationFrame(animate);
  animationFrames.value.push(frameId);
}

const createLoadingAnimation = (points) => {
  // 使用新的轮廓动画替代原来的中心点动画
  createOutlineAnimation(points, 2500);
}

const stopAllLoadingAnimations = () => {
  // 停止所有动画帧
  animationFrames.value.forEach(frameId => {
    cancelAnimationFrame(frameId);
  });
  animationFrames.value = [];

  // 隐藏所有光点
  loadingAnimations.value.forEach(animation => {
    animation.visible = false;
  });

  // 0.5秒后清空数组
  setTimeout(() => {
    loadingAnimations.value = [];
  }, 500);
}

const drawEyeOutline = (ctx, points, color = '#00ffaa') => {
  ctx.beginPath();
  ctx.strokeStyle = color;
  ctx.lineWidth = 2;

  // 移动到第一个点
  ctx.moveTo(points[0].x, points[0].y);

  // 连接所有点
  for (let i = 1; i < points.length; i++) {
    ctx.lineTo(points[i].x, points[i].y);
  }

  // 闭合路径
  ctx.closePath();
  ctx.stroke();

  // 绘制点标记
  points.forEach(point => {
    ctx.beginPath();
    ctx.arc(point.x, point.y, 2, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.fill();
  });
}

// 绘制下巴角度的函数
const drawChinAngle = (ctx, landmarks, chinAngle, color = '#ff6b6b') => {
  if (!landmarks || !chinAngle || landmarks.length < 17) {
    console.log('下巴角度数据不完整', landmarks?.length);
    return;
  }

  console.log('开始绘制下巴角度，landmarks数量:', landmarks.length);
  console.log('前几个landmarks点:', landmarks.slice(0, 15));

  // 获取第6、9、12个点（landmarks数组中id为6、9、12的点）
  const point6 = landmarks.find(p => p.id === 6);  // 第6个点
  const point9 = landmarks.find(p => p.id === 9);  // 第9个点（中心点）
  const point12 = landmarks.find(p => p.id === 12); // 第12个点

  if (!point6 || !point9 || !point12) {
    console.log('下巴角度关键点不存在', { point6, point9, point12 });
    return;
  }

  console.log('找到关键点:', { point6, point9, point12 });

  // 设置线条样式
  ctx.strokeStyle = color;
  ctx.lineWidth = 3;
  ctx.globalAlpha = 0.8;

  // 绘制第一条线：第6个点到第9个点
  ctx.beginPath();
  ctx.moveTo(point6.x, point6.y);
  ctx.lineTo(point9.x, point9.y);
  ctx.stroke();

  // 绘制第二条线：第12个点到第9个点
  ctx.beginPath();
  ctx.moveTo(point12.x, point12.y);
  ctx.lineTo(point9.x, point9.y);
  ctx.stroke();

  // 绘制关键点
  [point6, point9, point12].forEach((point, index) => {
    ctx.beginPath();
    ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.fill();

    // 添加点的标识
    ctx.fillStyle = '#fff';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    const labels = ['P6', 'P9', 'P12'];
    ctx.fillText(labels[index], point.x, point.y - 8);
  });

  // 计算角度弧线的绘制
  const vector1 = { x: point6.x - point9.x, y: point6.y - point9.y };
  const vector2 = { x: point12.x - point9.x, y: point12.y - point9.y };

  // 计算向量的角度
  const angle1 = Math.atan2(vector1.y, vector1.x);
  const angle2 = Math.atan2(vector2.y, vector2.x);

  // 确保角度差在0-π之间
  let angleDiff = angle2 - angle1;
  if (angleDiff < 0) angleDiff += 2 * Math.PI;
  if (angleDiff > Math.PI) angleDiff = 2 * Math.PI - angleDiff;

  // 绘制角度弧线
  const arcRadius = 40;
  ctx.beginPath();
  ctx.arc(point9.x, point9.y, arcRadius, Math.min(angle1, angle2), Math.max(angle1, angle2));
  ctx.strokeStyle = color;
  ctx.lineWidth = 2;
  ctx.stroke();

  // 在弧线中间显示角度数值
  const midAngle = (angle1 + angle2) / 2;
  const textRadius = arcRadius + 15;
  const textX = point9.x + Math.cos(midAngle) * textRadius;
  const textY = point9.y + Math.sin(midAngle) * textRadius;

  // 绘制角度文本背景
  ctx.fillStyle = 'rgba(255, 107, 107, 0.8)';
  const text = `${chinAngle.toFixed(1)}°`;
  ctx.font = 'bold 14px Arial';
  const textWidth = ctx.measureText(text).width;
  ctx.fillRect(textX - textWidth/2 - 5, textY - 10, textWidth + 10, 20);

  // 绘制角度文本
  ctx.fillStyle = '#fff';
  ctx.textAlign = 'center';
  ctx.fillText(text, textX, textY + 4);

  // 重置透明度
  ctx.globalAlpha = 1;
}

// 缓动函数 - easeOutCubic
const easeOutCubic = (t) => {
  return 1 - Math.pow(1 - t, 3);
}

// 绘制点和连线的动画函数（支持局部放大）
const drawPointsAndLinesWithAnimation = (ctx, points, color, onComplete, zoomMode = true) => {
  const pointDuration = 200; // 每个点的绘制时间
  const lineDuration = 300;  // 每条线的绘制时间
  const pointDelay = 100;    // 点之间的延迟
  const lineDelay = 150;     // 线之间的延迟

  let drawnPoints = [];
  const canvasWidth = parseInt(canvasStyle.value.width.replace('px', ''));
  const canvasHeight = parseInt(canvasStyle.value.height.replace('px', ''));

  // 计算放大参数
  const boundingBox = zoomMode ? calculateBoundingBox(points) : null;
  const zoomParams = boundingBox ? calculateZoomParams(boundingBox, canvasWidth, canvasHeight) : null;

  // 转换点坐标到放大后的坐标系
  const transformedPoints = zoomMode && boundingBox && zoomParams
    ? points.map(point => transformPointToZoomed(point, boundingBox, zoomParams))
    : points;

  // 绘制单个点的动画
  const drawPointAnimation = (pointIndex) => {
    if (pointIndex >= transformedPoints.length) {
      // 所有点绘制完成，开始绘制连线
      setTimeout(() => drawLinesAnimation(), 200);
      return;
    }

    const point = transformedPoints[pointIndex];
    const startTime = Date.now();

    const animatePoint = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / pointDuration, 1);
      const easedProgress = easeOutCubic(progress);

      // 清除canvas（图片已经通过CSS变换同步放大）
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      // 重绘之前已完成的点（使用放大后的尺寸）
      drawnPoints.forEach(p => {
        const pointSize = zoomMode ? 3 : 2;
        const glowSize = zoomMode ? 5 : 4;

        ctx.beginPath();
        ctx.arc(p.x, p.y, pointSize, 0, Math.PI * 2);
        ctx.fillStyle = color;
        ctx.fill();

        // 添加发光效果
        ctx.beginPath();
        ctx.arc(p.x, p.y, glowSize, 0, Math.PI * 2);
        ctx.strokeStyle = color;
        ctx.lineWidth = 1;
        ctx.globalAlpha = 0.3;
        ctx.stroke();
        ctx.globalAlpha = 1;
      });

      // 绘制当前动画中的点（放大模式下使用更大的尺寸）
      const baseRadius = zoomMode ? 4 : 3;
      const baseGlowRadius = zoomMode ? 8 : 6;

      const currentRadius = baseRadius * easedProgress;
      const glowRadius = baseGlowRadius * easedProgress;

      if (currentRadius > 0) {
        // 主点
        ctx.beginPath();
        ctx.arc(point.x, point.y, currentRadius, 0, Math.PI * 2);
        ctx.fillStyle = color;
        ctx.fill();

        // 发光效果
        ctx.beginPath();
        ctx.arc(point.x, point.y, glowRadius, 0, Math.PI * 2);
        ctx.strokeStyle = color;
        ctx.lineWidth = 3;
        ctx.globalAlpha = 0.6 * easedProgress;
        ctx.stroke();
        ctx.globalAlpha = 1;

        // 额外的脉冲效果
        ctx.beginPath();
        ctx.arc(point.x, point.y, glowRadius * 1.5, 0, Math.PI * 2);
        ctx.strokeStyle = color;
        ctx.lineWidth = 1;
        ctx.globalAlpha = 0.2 * easedProgress;
        ctx.stroke();
        ctx.globalAlpha = 1;
      }

      ctx.draw(true);

      if (progress < 1) {
        requestAnimationFrame(animatePoint);
      } else {
        // 当前点绘制完成
        drawnPoints.push(point);
        setTimeout(() => drawPointAnimation(pointIndex + 1), pointDelay);
      }
    };

    requestAnimationFrame(animatePoint);
  };

  // 绘制连线的动画
  const drawLinesAnimation = () => {

    const drawLineAnimation = (lineIndex) => {
      if (lineIndex >= transformedPoints.length) {
        // 所有线绘制完成
        if (onComplete) onComplete();
        return;
      }

      const startPoint = transformedPoints[lineIndex];
      const endPoint = transformedPoints[(lineIndex + 1) % transformedPoints.length]; // 闭合连线
      const startTime = Date.now();

      const animateLine = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / lineDuration, 1);
        const easedProgress = easeOutCubic(progress);

        // 清除canvas（图片已经通过CSS变换同步放大）
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);

        // 重绘所有点（使用放大后的尺寸）
        drawnPoints.forEach(p => {
          const pointSize = zoomMode ? 3 : 2;
          const glowSize = zoomMode ? 5 : 4;

          ctx.beginPath();
          ctx.arc(p.x, p.y, pointSize, 0, Math.PI * 2);
          ctx.fillStyle = color;
          ctx.fill();

          // 点的发光效果
          ctx.beginPath();
          ctx.arc(p.x, p.y, glowSize, 0, Math.PI * 2);
          ctx.strokeStyle = color;
          ctx.lineWidth = 1;
          ctx.globalAlpha = 0.3;
          ctx.stroke();
          ctx.globalAlpha = 1;
        });

        // 重绘已完成的线（使用放大后的线宽）
        const lineWidth = zoomMode ? 2 : 1;
        const glowWidth = zoomMode ? 4 : 3;

        for (let i = 0; i < lineIndex; i++) {
          const start = transformedPoints[i];
          const end = transformedPoints[(i + 1) % transformedPoints.length];
          ctx.beginPath();
          ctx.setLineDash([10, 15])
          ctx.moveTo(start.x, start.y);
          ctx.lineTo(end.x, end.y);
          ctx.strokeStyle = color;
          ctx.lineWidth = lineWidth;
          ctx.stroke();

          // 添加线的发光效果
          ctx.beginPath();
          ctx.moveTo(start.x, start.y);
          ctx.lineTo(end.x, end.y);
          ctx.strokeStyle = color;
          ctx.lineWidth = glowWidth;
          ctx.globalAlpha = 0.2;
          ctx.stroke();
          ctx.globalAlpha = 1;
        }

        // 绘制当前动画中的线
        const currentX = startPoint.x + (endPoint.x - startPoint.x) * easedProgress;
        const currentY = startPoint.y + (endPoint.y - startPoint.y) * easedProgress;

        // 主线
        ctx.beginPath();
        ctx.moveTo(startPoint.x, startPoint.y);
        ctx.lineTo(currentX, currentY);
        ctx.strokeStyle = color;
        ctx.lineWidth = lineWidth;
        ctx.stroke();

        // 发光效果
        ctx.beginPath();
        ctx.moveTo(startPoint.x, startPoint.y);
        ctx.lineTo(currentX, currentY);
        ctx.strokeStyle = color;
        ctx.lineWidth = glowWidth;
        ctx.globalAlpha = 0.4;
        ctx.stroke();
        ctx.globalAlpha = 1;

        ctx.draw(true);

        if (progress < 1) {
          requestAnimationFrame(animateLine);
        } else {
          // 当前线绘制完成
          setTimeout(() => drawLineAnimation(lineIndex + 1), lineDelay);
        }
      };

      requestAnimationFrame(animateLine);
    };

    drawLineAnimation(0);
  };

  // 开始绘制点的动画
  drawPointAnimation(0);
}

// 计算部位的边界框
const calculateBoundingBox = (points) => {
  if (!points || points.length === 0) return null;

  let minX = points[0].x, maxX = points[0].x;
  let minY = points[0].y, maxY = points[0].y;

  points.forEach(point => {
    minX = Math.min(minX, point.x);
    maxX = Math.max(maxX, point.x);
    minY = Math.min(minY, point.y);
    maxY = Math.max(maxY, point.y);
  });

  // 添加一些边距
  const padding = 30;
  return {
    x: minX - padding,
    y: minY - padding,
    width: maxX - minX + padding * 2,
    height: maxY - minY + padding * 2,
    centerX: (minX + maxX) / 2,
    centerY: (minY + maxY) / 2,
    originalMinX: minX,
    originalMaxX: maxX,
    originalMinY: minY,
    originalMaxY: maxY
  };
};

// 计算放大参数
const calculateZoomParams = (boundingBox, canvasWidth, canvasHeight) => {
  // 目标放大区域大小（占canvas的60%）
  const targetWidth = canvasWidth * 0.6;
  const targetHeight = canvasHeight * 0.6;

  // 计算放大倍数
  const scaleX = targetWidth / boundingBox.width;
  const scaleY = targetHeight / boundingBox.height;
  const scale = Math.min(scaleX, scaleY, 3); // 最大放大3倍

  // 计算放大后的尺寸
  const zoomedWidth = boundingBox.width * scale;
  const zoomedHeight = boundingBox.height * scale;

  // 计算居中位置
  const zoomedX = (canvasWidth - zoomedWidth) / 2;
  const zoomedY = (canvasHeight - zoomedHeight) / 2;

  return {
    scale,
    zoomedX,
    zoomedY,
    zoomedWidth,
    zoomedHeight
  };
};

// 将原始坐标转换为放大后的坐标
const transformPointToZoomed = (point, boundingBox, zoomParams) => {
  // 计算点在原始边界框中的相对位置
  const relativeX = (point.x - boundingBox.x) / boundingBox.width;
  const relativeY = (point.y - boundingBox.y) / boundingBox.height;

  // 转换到放大后的坐标系
  return {
    x: zoomParams.zoomedX + relativeX * zoomParams.zoomedWidth,
    y: zoomParams.zoomedY + relativeY * zoomParams.zoomedHeight
  };
};

// 应用图片和canvas的同步放大效果
const applyZoomedEffect = (boundingBox, zoomParams, canvasWidth, canvasHeight) => {
  // 计算图片需要的变换参数
  const imageTransform = calculateImageTransform(boundingBox, zoomParams, canvasWidth, canvasHeight);

  // 应用图片变换
  imageStyle.value = {
    width: `${imageTransform.width}px`,
    height: `${imageTransform.height}px`,
    transform: `translate(${imageTransform.translateX}px, ${imageTransform.translateY}px) scale(${imageTransform.scale})`,
    transformOrigin: 'top left',
    transition: 'all 0.5s ease-in-out'
  };
};

// 计算图片变换参数
const calculateImageTransform = (boundingBox, zoomParams, canvasWidth, canvasHeight) => {
  // 计算原始图片在canvas中的位置和缩放
  const originalImageWidth = canvasWidth;
  const originalImageHeight = canvasHeight;

  // 计算需要放大的区域在原图中的相对位置
  const cropX = boundingBox.x / canvasWidth;
  const cropY = boundingBox.y / canvasHeight;

  // 计算图片的缩放和位移
  const scale = zoomParams.scale;
  const translateX = -cropX * originalImageWidth * scale + zoomParams.zoomedX;
  const translateY = -cropY * originalImageHeight * scale + zoomParams.zoomedY;

  return {
    width: originalImageWidth,
    height: originalImageHeight,
    scale: scale,
    translateX: translateX,
    translateY: translateY
  };
};

// 重置图片样式
const resetImageStyle = () => {
  imageStyle.value = {
    width: '100%',
    transform: 'none',
    transition: 'all 0.5s ease-in-out'
  };
};

// 创建局部放大的聚焦效果（仅绘制canvas装饰）
const createZoomedFocusEffect = (ctx, zoomParams, canvasWidth, canvasHeight) => {
  // 绘制放大区域的边框
  ctx.strokeStyle = '#00ffaa';
  ctx.lineWidth = 3;
  ctx.globalAlpha = 0.8;
  ctx.strokeRect(zoomParams.zoomedX, zoomParams.zoomedY, zoomParams.zoomedWidth, zoomParams.zoomedHeight);

  // 绘制装饰边框
  ctx.strokeStyle = '#00ffaa';
  ctx.lineWidth = 1;
  ctx.globalAlpha = 0.6;
  ctx.strokeRect(zoomParams.zoomedX - 2, zoomParams.zoomedY - 2, zoomParams.zoomedWidth + 4, zoomParams.zoomedHeight + 4);

  // 绘制四个角的装饰
  const cornerSize = 15;
  ctx.strokeStyle = '#00ffaa';
  ctx.lineWidth = 4;
  ctx.globalAlpha = 0.9;

  const x = zoomParams.zoomedX;
  const y = zoomParams.zoomedY;
  const w = zoomParams.zoomedWidth;
  const h = zoomParams.zoomedHeight;

  // 左上角
  ctx.beginPath();
  ctx.moveTo(x, y + cornerSize);
  ctx.lineTo(x, y);
  ctx.lineTo(x + cornerSize, y);
  ctx.stroke();

  // 右上角
  ctx.beginPath();
  ctx.moveTo(x + w - cornerSize, y);
  ctx.lineTo(x + w, y);
  ctx.lineTo(x + w, y + cornerSize);
  ctx.stroke();

  // 左下角
  ctx.beginPath();
  ctx.moveTo(x, y + h - cornerSize);
  ctx.lineTo(x, y + h);
  ctx.lineTo(x + cornerSize, y + h);
  ctx.stroke();

  // 右下角
  ctx.beginPath();
  ctx.moveTo(x + w - cornerSize, y + h);
  ctx.lineTo(x + w, y + h);
  ctx.lineTo(x + w, y + h - cornerSize);
  ctx.stroke();

  // 添加放大倍数标识
  ctx.globalAlpha = 0.7;
  ctx.fillStyle = '#00ffaa';
  ctx.font = '12px Arial';
  ctx.textAlign = 'right';
  ctx.fillText(`${zoomParams.scale.toFixed(1)}x`, x + w - 10, y + 20);

  // 重置透明度
  ctx.globalAlpha = 1;
};

const initCanvasDrawing = (width, height, landmarks) => {
  // 设置canvas样式
  canvasStyle.value = {
    width: width + 'px',
    height: height + 'px'
  };

  showCanvas.value = true;

  // 等待canvas渲染完成后绘制
  nextTick(() => {
    setTimeout(() => {
      const ctx = uni.createCanvasContext('eyeCanvas');
      const canvasWidth = parseInt(width);
      const canvasHeight = parseInt(height);

      // 定义需要绘制的部位配置（按照landmarks对象的顺序）
      const facialParts = [
        { name: 'jaw', color: '#00ffaa', label: '下颌轮廓' },
        { name: 'right_eyebrow', color: '#ff6b6b', label: '右眉毛' },
        { name: 'left_eyebrow', color: '#4ecdc4', label: '左眉毛' },
        { name: 'nose_bridge', color: '#45b7d1', label: '鼻梁' },
        { name: 'nose_tip', color: '#96ceb4', label: '鼻尖' },
        { name: 'right_eye', color: '#feca57', label: '右眼' },
        { name: 'left_eye', color: '#ff9ff3', label: '左眼' },
        { name: 'outer_lips', color: '#54a0ff', label: '外唇轮廓' },
        { name: 'inner_lips', color: '#5f27cd', label: '内唇轮廓' }
      ];

      // 存储已绘制的部位
      let drawnParts = [];

      // 动画绘制每个部位
      let currentPartIndex = 0;

      const drawPartWithAnimation = () => {
        if (currentPartIndex >= facialParts.length) {
          // 所有部位绘制完成，重置图片和canvas，显示完整图像
          setTimeout(() => {
            // 重置图片样式
            resetImageStyle();

            // 清除canvas并重绘所有部位
            ctx.clearRect(0, 0, canvasWidth, canvasHeight);
            drawnParts.forEach(part => {
              drawEyeOutline(ctx, part.points, part.color);
            });

            // 绘制下巴角度
            if (chinAngleData.value) {
              console.log('最终绘制下巴角度:', chinAngleData.value);
              // 获取完整的landmarks数组
              const detectInfo = uni.getStorageSync('detectInfo');
              if (detectInfo && detectInfo.detect && detectInfo.detect.landmarks) {
                const allLandmarks = detectInfo.detect.landmarks.map(landmark => ({
                  x: landmark.x * imageWidthRatio.value,
                  y: landmark.y * imageHeightRatio.value,
                  id: landmark.id
                }));
                drawChinAngle(ctx, allLandmarks, chinAngleData.value);
              }
            }

            ctx.draw();
          }, 500);
          return;
        }

        const currentPart = facialParts[currentPartIndex];
        const partData = landmarks[currentPart.name];

        if (!partData || partData.length === 0) {
          console.log(`${currentPart.name} 数据不存在或为空`);
          currentPartIndex++;
          setTimeout(drawPartWithAnimation, 500);
          return;
        }

        console.log(`开始绘制 ${currentPart.label}，包含 ${partData.length} 个点`);

        // 计算当前部位的边界框和放大参数
        const boundingBox = calculateBoundingBox(partData);
        const zoomParams = boundingBox ? calculateZoomParams(boundingBox, canvasWidth, canvasHeight) : null;

        if (boundingBox && zoomParams) {
          // 应用图片和canvas的同步放大效果
          applyZoomedEffect(boundingBox, zoomParams, canvasWidth, canvasHeight);

          // 清除画布
          ctx.clearRect(0, 0, canvasWidth, canvasHeight);

          // 创建放大聚焦效果（仅canvas装饰）
          createZoomedFocusEffect(ctx, zoomParams, canvasWidth, canvasHeight);

          // 绘制部位标签（在放大区域上方）
          ctx.globalAlpha = 0.9;
          ctx.fillStyle = '#00ffaa';
          const labelWidth = currentPart.label.length * 14 + 30;
          const labelHeight = 30;
          const labelX = zoomParams.zoomedX + (zoomParams.zoomedWidth - labelWidth) / 2;
          const labelY = zoomParams.zoomedY - 40;
          ctx.fillRect(labelX, labelY, labelWidth, labelHeight);

          // 标签文字
          ctx.globalAlpha = 1;
          ctx.fillStyle = '#fff';
          ctx.font = 'bold 16px Arial';
          ctx.textAlign = 'center';
          ctx.fillText(currentPart.label, zoomParams.zoomedX + zoomParams.zoomedWidth / 2, labelY + 20);

          // 显示放大倍数
          ctx.globalAlpha = 0.7;
          ctx.fillStyle = '#00ffaa';
          ctx.font = '12px Arial';
          ctx.textAlign = 'left';
          ctx.fillText(`放大 ${zoomParams.scale.toFixed(1)}x`, zoomParams.zoomedX + 10, zoomParams.zoomedY + 25);

          ctx.globalAlpha = 1;
          ctx.draw(true);
        }

        // 绘制当前部位的点和连线动画（使用放大模式）
        drawPointsAndLinesWithAnimation(ctx, partData, currentPart.color, () => {
          // 将当前部位添加到已绘制列表
          drawnParts.push({
            points: partData,
            color: currentPart.color,
            name: currentPart.name
          });

          // 如果当前绘制的是下颌轮廓，立即绘制下巴角度
          if (currentPart.name === 'jaw' && chinAngleData.value) {
            console.log('下颌轮廓绘制完成，绘制下巴角度:', chinAngleData.value);
            setTimeout(() => {
              // 获取完整的landmarks数组（从原始detectInfo中获取）
              const detectInfo = uni.getStorageSync('detectInfo');
              if (detectInfo && detectInfo.detect && detectInfo.detect.landmarks) {
                const allLandmarks = detectInfo.detect.landmarks.map(landmark => ({
                  x: landmark.x * imageWidthRatio.value,
                  y: landmark.y * imageHeightRatio.value,
                  id: landmark.id
                }));
                drawChinAngle(ctx, allLandmarks, chinAngleData.value);
                ctx.draw(true);
              }
            }, 300);
          }

          currentPartIndex++;
          setTimeout(drawPartWithAnimation, 1200); // 每个部位完成后延迟1.2秒绘制下一个
        }, true); // 启用放大模式
      };

      // 开始动画绘制
      drawPartWithAnimation();

    }, 100);
  });
}

const handlePreview = () => {
  // 防止iOS端快速点击导致的状态混乱
  if (isAnimating.value) return

  // 检查用户是否已登录
  const userInfo = uni.getStorageSync('userInfo')

  if (!userInfo) {
    // 未登录，通知父组件显示登录弹窗
    emit('show-login')
    return
  }

  // 已登录，执行原有逻辑
  expandPreview()
}

const expandPreview = () => {
  // if (beforeimgRef.value) {
  //   beforeImgHeight.value = beforeimgRef.value.height
  // }

  // 添加动画状态控制
  isAnimating.value = true
  isExpanded.value = !isExpanded.value

  // 动画完成后重置状态
  setTimeout(() => {
    isAnimating.value = false
  }, 300)
}
let face_aes = ref({})
let detect = ref({})
let generate = ref({})
async function getStatus(operationId) {
  let { data } = await callAiPolling({ operationId, type: 'esthetics' })

  // 计算进度：aiStatus为'1'的数量 / 总数量 * 100
  const completedCount = data.data.filter(item => item.aiStatus == '1').length
  const totalCount = data.data.length
  const percent = Math.round((completedCount / totalCount) * 100)

  // 更新进度
  emit('update:percent', percent)

  // let status = data.data.every(item => item.aiStatus == '1')
  let generateStatus = data.data.filter(item => item.aiType == 'generate')[0].aiStatus
  let faceAesStatus = data.data.filter(item => item.aiType == 'face_aes')[0].aiStatus
  if (faceAesStatus == '1') {
    face_aes.value = JSON.parse(data.data.filter(item => item.aiType == 'face_aes')[0].aiResult)

  } else if (faceAesStatus == '2') {
    // uni.showModal({
    //   title: '提示',
    //   content: '生成图片失败',
    //   confirmText:"重新上传",
    //   success: function (res) {
    //     if (res.confirm) {
    //       uni.navigateBack();
    //     } else if (res.cancel) {
    //       console.log('用户点击取消');
    //     }
    //   }
    // });

    // uni.showToast({
    //   title: '生成图片失败',
    //   duration: 2000,
    //   icon:"none"
    // });
    // clearInterval(timer)
    // stopAllLoadingAnimations()
  }
  let status = data.data.every(item => item.aiStatus == '1')
  if (status) {

    clearInterval(timer)
  }
  if (generateStatus == '1') {
    generate.value = data.data.filter(item => item.aiType == 'generate')[0].aiResult
    // 完成后隐藏loading
    // emit('update:loading', false)
    // emit('update:percent', 100)

    // 10秒后停止加载动画
    stopAllLoadingAnimations()
  } else if (generateStatus == '2') {
    // uni.showModal({
    //   title: '提示',
    //   content: '生成图片失败',
    //   confirmText:"重新上传",
    //   success: function (res) {
    //     if (res.confirm) {
    //       uni.navigateBack();
    //     } else if (res.cancel) {
    //       console.log('用户点击取消');
    //     }
    //   }
    // });

    // uni.showToast({
    //   title: '生成图片失败',
    //   duration: 2000,
    //   icon:"none"
    // });
    // clearInterval(timer)
    // stopAllLoadingAnimations()
  }
}

function organizeData(data) {

  face_aes.value = JSON.parse(data.filter(item => item.aiType == 'face_aes')[0].aiResult)
  detect.value = JSON.parse(data.filter(item => item.aiType == 'detect')[0].aiResult)
  generate.value = data.filter(item => item.aiType == 'generate')[0].aiResult

}
const popupVisible = ref(false)
const selectIcon2 = (item) => {
  // activeReport.value = item;
  emit('update:activeReport', item)

  // 重置展开状态，确保每次切换tab时都是收起状态
  isExpanded.value = false
  isAnimating.value = false

  // iOS端需要更长的延迟来确保DOM更新完成
  setTimeout(() => {
    popupVisible.value = true;
  }, 100);
}

const gotoOrgList = () => {
  uni.navigateTo({
    url: '/pages/orgList/index'
  })
}


// 推荐项目数据
const recommendedItems = ref([
  {
    imgSrc: '/static/imgs/ject1.jpg',
    alt: "Side profile of a young woman, neutral expression, white background",
    title: "明星医师方案",
    category: "玻尿酸+肉毒素+光子嫩肤",
  },
  {
    imgSrc: '/static/imgs/ject2.jpg',
    alt: "Side profile of a young woman, neutral expression, white background",
    title: "黄金标准方案",
    category: "玻尿酸+肉毒素",
  },
  {
    imgSrc: '/static/imgs/ject3.jpg',
    alt: "Side profile of a young woman, neutral expression, white background",
    title: "经典臻选方案",
    category: "玻尿酸",
  },
]);



watch(() => props.activeReport, () => {
  // 监听activeReport变化，重置展开状态
  isExpanded.value = false
  isAnimating.value = false
}, { deep: true });

// 暴露方法给父组件调用
defineExpose({
  expandPreview
})
</script>

<style lang="scss" scoped>
/* 标签样式 */
.tag {
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

/* 弹窗样式 */
.popup-overlay {

  width: 100%;
  height: 100%;
  // background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

.popup-container {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 4;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  /* iOS兼容性：使用fallback背景色 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-top-left-radius: 5rpx;
  border-top-right-radius: 5rpx;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 110rpx;
  margin-bottom: calc(110rpx + constant(safe-area-inset-bottom));
  /* 兼容 IOS<11.2 */
  margin-bottom: calc(110rpx + env(safe-area-inset-bottom));
  /* 兼容 IOS>11.2 */
  padding-bottom: env(safe-area-inset-bottom);
  /* iOS安全区域适配 */
  overflow: hidden;
  will-change: transform, height;
  /* iOS滚动优化 */
  -webkit-overflow-scrolling: touch;
  /* iOS点击优化 */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  /* 强制硬件加速 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);

  &>view {
    height: 100%;
    display: flex;
    flex-direction: column;

    .popup-content {
      flex: 1;
      overflow-y: auto;
      /* iOS滚动优化 */
      -webkit-overflow-scrolling: touch;
    }
  }
}

.popup-container.collapsed {
  height: 200rpx;
  overflow: hidden;
}

.collapsed {
  .popup-content {
    overflow: hidden !important;
  }
}

.popup-container.expanded {
  max-height: 60vh;
  height: auto;
  overflow-y: auto;
  min-height: 0;
}

.popup-container.slide-up {
  transform: translateY(0) translateZ(0);
  /* iOS优化：强制硬件加速 */
  -webkit-transform: translateY(0) translateZ(0);
}

.popup-container.slide-down {
  transform: translateY(100%) translateZ(0);
  /* iOS优化：强制硬件加速 */
  -webkit-transform: translateY(100%) translateZ(0);
}

.popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
}

.popup-content {
  width: 100%;
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  transition: none;
  /* 移除内容区域的过渡动画 */
  /* iOS滚动优化 */
  -webkit-overflow-scrolling: touch;
}

.popup-container.expanded .popup-content {
  overflow-y: auto;
}

.popup-container.collapsed .popup-content {
  overflow-y: auto;
}

/* 横向滚动样式 */
scroll-view ::v-deep {
  .flex {
    flex-wrap: nowrap;
  }
}

/* 底部标签栏样式 */
.bottom-tab {
  position: relative;
}

.bottom-tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 4rpx;
  background-color: #ff4d4f;
}

.inactive-tab {
  color: #999;
}

/* 新增iOS适配样式 */
.ios-safe-area {
  padding-top: env(safe-area-inset-top);
}

.ios-bottom-safe {
  padding-bottom: env(safe-area-inset-bottom);
}

/* iOS滚动优化 */
scroll-view {
  -webkit-overflow-scrolling: touch;
}

/* iOS按钮点击效果优化 */
button {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* iOS特定按钮样式 */
.ios-button {
  /* 防止iOS双击缩放 */
  touch-action: manipulation;
  /* 强制硬件加速 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  /* 防止点击延迟 */
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  /* 防止长按选择 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* 防止拖拽 */
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
}

/* iOS图片优化 */
img {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* Canvas和光点动画样式 */
.image-container {
  position: relative;
  display: inline-block;
  overflow: hidden; /* 防止聚焦效果溢出 */
}

.light-point {
  position: absolute;
  width: 12px;
  height: 12px;
  background: #00ffaa;
  border-radius: 50%;
  box-shadow: 0 0 15px #00ffaa;
  transform: translate(-50%, -50%);
  z-index: 3;
  transition: all 0.05s linear;
}

/* Canvas聚焦动画样式 */
#eyeCanvas {
  transition: all 0.3s ease-in-out;
  will-change: transform;
}

/* 聚焦时的脉冲动画 */
@keyframes focusPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 255, 170, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 255, 170, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 255, 170, 0);
  }
}

.focus-active {
  animation: focusPulse 2s infinite;
}
</style>